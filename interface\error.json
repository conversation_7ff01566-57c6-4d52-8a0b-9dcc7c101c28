[{"arrayIndex": 97, "objectNumber": 98, "object": {"chef_5188800": "Hormel Sausage Chorizo Crumbled Pork Cooked", "sham_2261341": "Sausage, Chorizo Cooked Crumble", "sham_3311471": "Sausage, Chorizo Pork Crumble Cooked Bag Frozen", "sysco_1467232": "Sausage Chorizo Crumble Cooked", "usfood_5188800": "Sausage, Chorizo Crumble Pork Cooked Frozen Topping", "greco_199092": "CHORIZO PORK STRIPS SAUSAGE FC", "greco_201709": "CHORIZO CRUMBLE ALL NATURAL FC", "greco_200340": "CHORIZO PRE-SLC 12/4oz #42432 VOLPIPRE-SLICED #42432", "greco_20357": "CHORIZO SPANISH  6/14OZ 5# CASE", "depot_1450117": "T&J Sausage - Pork Chorizo - 5 lbs", "depot_20576": "Reynaldo's - Pork Chorizo - 11 Oz", "depot_1450974": "Reynaldo- Premium Chorizo", "depot_52224": "<PERSON><PERSON><PERSON>'s Mexican Food - Pork Chorizo - 2.5 lb", "perf_020043": "CHOR<PERSON>ZO FULLY-COOKED TOPPING FROZEN", "perf_416494": "SAUSAGE CRUMBLE FULLY-COOKED FROZEN", "greco_201603": "SAUSAGE TOPPING <PERSON><PERSON> COOKED 2/10#", "greco_200660": "SAUSAGE PIZZA ITL CKD TPG 3/5# RP-3", "greco_20087": "TOPPING PORK PIZZANO ITAL 2/5# 1548", "greco_201486": "SAUSAGE TOPING 15# P2232 FONTANNI", "greco_201014": "SAUSAGE CRUMBLED COOKED 10# 41141", "perf_066829": "PORK TOPPING CRUMBLE ITALIAN FULLY-COOKED FROZEN 60 NUGGET PER OUNCE", "perf_066828": "PORK TOPPING ITALIAN FROZEN FULLY-COOKED 14 NUGGET PER OUNCE", "perf_072333": "PORK TOPPING SPICY FULLY-COOKED <PERSON>ROZEN SPICY FROZEN FULLY-COOKED 42 OUNCE", "greco_199213": "TOPPING SAUSAGE BREAKFAST CRMBL FC", "greco_199102": "TOPPING SAUS CKD ITAL STYLE 2/5#", "greco_504922": "SAUSAGE TOPPING CHUNKY MILD #40969", "perf_021944": "TOPPING CRUMBLE SAUSAGE ITALIAN FROZEN SPICY", "perf_021057": "PORK TOPPING FULLY-COOKED CHUNK FROZEN"}, "error": "{\"error\":{\"message\":\"{\\n  \\\"error\\\": {\\n    \\\"code\\\": 503,\\n    \\\"message\\\": \\\"The model is overloaded. Please try again later.\\\",\\n    \\\"status\\\": \\\"UNAVAILABLE\\\"\\n  }\\n}\\n\",\"code\":503,\"status\":\"Service Unavailable\"}}", "timestamp": "2025-07-01T05:12:32.019Z"}, {"arrayIndex": 97, "objectNumber": 98, "object": {"chef_5188800": "Hormel Sausage Chorizo Crumbled Pork Cooked", "sham_2261341": "Sausage, Chorizo Cooked Crumble", "sham_3311471": "Sausage, Chorizo Pork Crumble Cooked Bag Frozen", "sysco_1467232": "Sausage Chorizo Crumble Cooked", "usfood_5188800": "Sausage, Chorizo Crumble Pork Cooked Frozen Topping", "greco_199092": "CHORIZO PORK STRIPS SAUSAGE FC", "greco_201709": "CHORIZO CRUMBLE ALL NATURAL FC", "greco_200340": "CHORIZO PRE-SLC 12/4oz #42432 VOLPIPRE-SLICED #42432", "greco_20357": "CHORIZO SPANISH  6/14OZ 5# CASE", "depot_1450117": "T&J Sausage - Pork Chorizo - 5 lbs", "depot_20576": "Reynaldo's - Pork Chorizo - 11 Oz", "depot_1450974": "Reynaldo- Premium Chorizo", "depot_52224": "<PERSON><PERSON><PERSON>'s Mexican Food - Pork Chorizo - 2.5 lb", "perf_020043": "CHOR<PERSON>ZO FULLY-COOKED TOPPING FROZEN", "perf_416494": "SAUSAGE CRUMBLE FULLY-COOKED FROZEN", "greco_201603": "SAUSAGE TOPPING <PERSON><PERSON> COOKED 2/10#", "greco_200660": "SAUSAGE PIZZA ITL CKD TPG 3/5# RP-3", "greco_20087": "TOPPING PORK PIZZANO ITAL 2/5# 1548", "greco_201486": "SAUSAGE TOPING 15# P2232 FONTANNI", "greco_201014": "SAUSAGE CRUMBLED COOKED 10# 41141", "perf_066829": "PORK TOPPING CRUMBLE ITALIAN FULLY-COOKED FROZEN 60 NUGGET PER OUNCE", "perf_066828": "PORK TOPPING ITALIAN FROZEN FULLY-COOKED 14 NUGGET PER OUNCE", "perf_072333": "PORK TOPPING SPICY FULLY-COOKED <PERSON>ROZEN SPICY FROZEN FULLY-COOKED 42 OUNCE", "greco_199213": "TOPPING SAUSAGE BREAKFAST CRMBL FC", "greco_199102": "TOPPING SAUS CKD ITAL STYLE 2/5#", "greco_504922": "SAUSAGE TOPPING CHUNKY MILD #40969", "perf_021944": "TOPPING CRUMBLE SAUSAGE ITALIAN FROZEN SPICY", "perf_021057": "PORK TOPPING FULLY-COOKED CHUNK FROZEN"}, "error": "{\"error\":{\"message\":\"{\\n  \\\"error\\\": {\\n    \\\"code\\\": 503,\\n    \\\"message\\\": \\\"The model is overloaded. Please try again later.\\\",\\n    \\\"status\\\": \\\"UNAVAILABLE\\\"\\n  }\\n}\\n\",\"code\":503,\"status\":\"Service Unavailable\"}}", "timestamp": "2025-07-01T05:13:52.224Z"}, {"arrayIndex": 100, "objectNumber": 101, "object": {"chef_6211098": "Carolina Turkey Turkey Breast Whole Dome Skinned", "usfood_6968994": "Turkey, Breast Whole Pan Skinless All Natural Cooked Fried Ref Unsliced", "depot_41045": "Foster Farms - Oil Browned Turkey Breast", "depot_40120": "Jennie -o - Deli Fav Oil -Browned Turkey Breast", "depot_41047": "Foster Farms - Browned <PERSON> Breast - 8-9 lbs avg, 2 per case", "greco_200804FP": "TURKEY BREAST OVEN PREPARED  81101", "greco_200812": "TURKEY BRST OVEN ROASTED #46802", "greco_200813": "TURKEY BRST GOLDEN BROWNED #47672", "greco_200813FZ": "TURKEY BRST GOLDEN BROWNED #47672", "greco_20086FRZ": "TURKEY BREAST OVEN RSTD NTRL 2/8#", "greco_68502": "TURKEY BREAST OVEN ROASTED", "depot_73492": "Jennie-O Turkey Breast Oven Roasted", "greco_20093": "TURKEY BREAST SMOKED HONEY MESQUITE", "depot_73495": "<PERSON>nie-O Turkey Breash Hickory Smoked Honey Roasted", "depot_55432": "<PERSON><PERSON> -O - Smoked Turkey Breast", "perf_699001": "TURKEY BREAST SKINLESS SMOKED GRAND CHAMPION", "greco_200923": "CHIC SLCD OVEN ROASTED #68982", "depot_41044": "Foster Farms Pan Roasted Turkey Breast", "perf_87418": "TURKEY BREAST BROWNED IN OIL SKINLESS PAN STYLE 3-4 PIECE FULLY-COOKED", "greco_200384": "TURKEY OVEN BRWND SLCD .67oz #48114"}, "error": "{\"error\":{\"message\":\"{\\n  \\\"error\\\": {\\n    \\\"code\\\": 503,\\n    \\\"message\\\": \\\"The model is overloaded. Please try again later.\\\",\\n    \\\"status\\\": \\\"UNAVAILABLE\\\"\\n  }\\n}\\n\",\"code\":503,\"status\":\"Service Unavailable\"}}", "timestamp": "2025-07-01T05:21:05.903Z"}, {"arrayIndex": 170, "objectNumber": 171, "object": {"chef_0675389": "Jolly Time Popcorn Raw Blast-O-Butter", "usfood_675389": "Popcorn, Raw Blast-o-butter Dispenser"}, "error": "{\"error\":{\"message\":\"{\\n  \\\"error\\\": {\\n    \\\"code\\\": 503,\\n    \\\"message\\\": \\\"The model is overloaded. Please try again later.\\\",\\n    \\\"status\\\": \\\"UNAVAILABLE\\\"\\n  }\\n}\\n\",\"code\":503,\"status\":\"Service Unavailable\"}}", "timestamp": "2025-07-01T07:01:39.802Z"}, {"arrayIndex": 192, "objectNumber": 193, "object": {"chef_1137747": "Little <PERSON> Big Pack Christmas Tree Cakes", "sham_2532881": "<PERSON><PERSON>, <PERSON> Tree Individually Wrapped 1.2oz"}, "error": "Invalid JSON response: Unexpected non-whitespace character after JSON at position 150 (line 6 column 2)", "timestamp": "2025-07-01T07:28:02.158Z"}, {"arrayIndex": 213, "objectNumber": 214, "object": {"chef_1005743": "<PERSON>, <PERSON><PERSON><PERSON>", "sham_1968021": "Gravy Mix, Country Style", "sham_1968031": "Gravy Mix, Country Style Powder", "sysco_1975812": "Mix Gravy Country", "sysco_4221560": "Mix Gravy Country Ztf 8/22 Oz", "sysco_4231254": "Mix Gravy Country Style", "sysco_5078308": "Mix Gravy Country Style", "sysco_6026154": "Mix Gravy Country", "sysco_6026160": "Mix Gravy Country", "sysco_6043151": "Mix Gravy Country", "usfood_1005743": "Mix, Gravy Country Style", "usfood_2009967": "Mix, Gravy Country Style", "depot_1060236": "Chef's Quality Country Gravy Mix - 24 oz", "depot_45280": "Pioneer - Country Gravy Mix - 24 oz", "perf_438991": "GRAVY MIX BROWN COUNTRY STYLE 0 GRAMS TRANS FAT PER SERVING REPLACES 997258", "perf_438985": "GRAVY MIX COUNTRY STYLE 0 GRAMS TRANS FAT PER SERVING REPLACES 999717", "perf_478192": "GRAVY MIX COUNTRY STYLE", "greco_201175": "GRAVY MIX COUNTRY 8/21.9# TRIO"}, "error": "Invalid JSON response: Unexpected non-whitespace character after JSON at position 988 (line 22 column 2)", "timestamp": "2025-07-01T07:39:44.809Z"}, {"arrayIndex": 224, "objectNumber": 225, "object": {"chef_3760923": "Monarch Lemon Pudding Ready To Use Can Shelf Stable", "sysco_4011052": "Pudding Lemon Ready-To-Use", "usfood_3760923": "<PERSON><PERSON>g, Lemon Rtu Can Shelf Stable"}, "error": "Invalid JSON response: Unexpected non-whitespace character after JSON at position 197 (line 7 column 2)", "timestamp": "2025-07-01T07:45:24.590Z"}, {"arrayIndex": 239, "objectNumber": 240, "object": {"chef_3737301": "Monarch Wildflower Honey", "sysco_5611652": "Honey Pure Wildflower Grade A True Source Certified In Jug", "sysco_6006165": "Honey Wildflower Blackland Prairie Lone Star Bee Company", "sysco_6006169": "Honey Wildflower 12 OZ Orchard Pond", "sysco_6006171": "<PERSON>, Honey Acres", "sysco_6006286": "Honey Georgia Wildflower, Zeigler Honey Company", "sysco_6008503": "Honey Wildflower Creamed 1 lb Orchard Pond", "sysco_6008520": "Honey Wildflower with <PERSON><PERSON>, Zeigler Honey Company", "sysco_6008523": "Honey Wildflower Blackland Prairie Lone Star Bee Company", "sysco_6008545": "Honey Raw Organic Wildflower, Boots Chicago", "sysco_6008552": "Honey Wildflower 1 lb Orchard Pond", "sysco_6025874": "Honey Wildflower", "sysco_6025888": "Honey Wildflower With Comb", "sysco_6032248": "Raw Wildflower Honey, Wild-crafted, Polyflora", "sysco_6037356": "Honey Wildflower Wildflower", "sysco_7136196": "Honeycomb Whole Wildflower Raw Natural Kosher", "usfood_3737301": "Honey, Wildflower Plastic Jug Shelf Stable Grade A", "usfood_6660517": "Honey, Wildflower Chili Hot Squeeze Bottle Shelf Stable", "usfood_9405756": "Honey, Wildflower Chili Hot Squeeze Bottle Shelf Stable Retail", "usfood_6938462": "Honey, Wildflower Amber Light Plastic Jug Shelf Stable Grade A", "usfood_1506075": "Honey, Wildflower Ss Pouch Shelf Stable", "usfood_1049994": "Honey, Wildflower Habanero Extra Hot Plastic Bottle Shelf Stable", "usfood_1049993": "Honey, Wildflower Sweet & Spicy Plastic Bottle Shelf Stable", "usfood_6739095": "<PERSON>, Wildflower  With Comb", "usfood_3703877": "<PERSON>, Wildflower Glass Jar Shelf Stable Whipped", "usfood_2899812": "<PERSON>, Wildflower Glass Jar Shelf Stable Apricot Whipped", "greco_3901388": "HONEY HOT BOTTLE 4/24oz MIKE", "depot_2510025": "Chef's Quality - Honey in Bear Squeeze Bottle - 12/12 oz", "depot_2490005": "Chef's Quality - Wildflower Honey - 5 lbs", "perf_mei00000": "HONEY HOT BOTTLE", "perf_566586": "HONEY HOT CHEF BOTTLE", "perf_104223": "HONEY HOT SQUEEZE PACK", "greco_39009": "HONEY 5# JUG WILDFLOWER", "depot_2910134": "Chef's Quality - Sweet Honey Blend, 5 lb", "depot_2330282": "<PERSON><PERSON>- Pure Honey Packaging - 200ct", "depot_22256": "Kraft - Pure Honey Packets - 200ct", "greco_39009C": "HONEY LIGHT AMBER WILDFLOWER", "perf_088565": "HONEY AMBER EXTRA LIGHT JUG 0 GRAMS TRANS FAT PER SERVING", "perf_1012654": "HONEY AMBER LIGHT", "perf_036077": "HONEY LIGHT AMBER", "perf_176076": "HONEY EXTRA HOT"}, "error": "Invalid JSON response: Unexpected non-whitespace character after JSON at position 2673 (line 53 column 2)", "timestamp": "2025-07-01T07:53:05.887Z"}, {"arrayIndex": 241, "objectNumber": 242, "object": {"chef_3007564": "Nabisco Graham Cracker Crumbs", "sham_1924321": "Topping, <PERSON> Crumb", "sham_1970671": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "sysco_4009601": "Cracker Crumbs Graham", "sysco_4091864": "Cracker Crumbs Graham", "usfood_3007564": "<PERSON><PERSON><PERSON>, <PERSON> Plain Fine Bag", "usfood_2017028": "<PERSON><PERSON><PERSON>, <PERSON> Plain Fine Bag", "usfood_5003983": "<PERSON><PERSON><PERSON>, <PERSON> Plain Fine Bag", "usfood_8652727": "<PERSON><PERSON><PERSON>, <PERSON> Plain Fine Gluten-free Box", "usfood_1166976": "<PERSON><PERSON><PERSON>, <PERSON> Plain Fine Gluten-free Box", "depot_1090063": "Chef's Quality - Graham Cracker Crumbs - 10 lbs", "greco_50496": "CRACKER GRAHAM CRUMBS 10#   15347"}, "error": "Invalid JSON response: Unexpected non-whitespace character after JSON at position 724 (line 18 column 2)", "timestamp": "2025-07-01T08:03:32.988Z"}, {"arrayIndex": 630, "objectNumber": 631, "object": {"sham_1933721": "Mush<PERSON>, Large White", "chef_7006497": "Large Fresh Mushrooms", "sham_1105281": "Mushroom, Shiitake Large", "sham_1108171": "Mushroom, Portabella Large", "sysco_1182195": "Mushroom Large Fresh", "sysco_5430756": "Mushrooms Jumbo Fresh", "usfood_9331497": "Mushroom, White Large #1 Grade Cultured Bulk Fresh Ref", "usfood_5367297": "Mushroom, Maitake Large Cultured Hen-of-the-woods Fresh Ref", "usfood_2517712": "Mushroom, Shiitake Large Cultured Fresh Ref", "greco_35011P": "MUSHROOMS WHL LARGE 1/10# 44446", "greco_60551SH": "MUSHROOM SHIITAKE CS", "perf_067351": "MUSHROOM SHIITAKE", "greco_60551": "MUSHROOM PORTABELLO CASE", "depot_42542": "<PERSON><PERSON><PERSON> 5 lbs", "perf_067138": "MUSHROOM PORTOBELLO CAP", "greco_60535": "MUSHROOM WHT WHOLE LARGE CS", "greco_60544": "MUSHROOM WHT WHL FOOD SRVC", "perf_590564": "MUSHROOM WHITE LARGE", "greco_60534": "MUSHROOM WHT WHOLE MEDIUM CS", "chef_8374258": "Mushroom Fresh", "sham_1105511": "Mushroom, Chanterelle", "sham_1907601": "Mushroom, White Med", "sham_1933691": "Mushroom, Button White", "sham_1933701": "Mushroom, Crimini", "sham_1933731": "<PERSON><PERSON><PERSON>, Chopper Mature", "sham_1933761": "Mushroom, Oyster Refrigerated Fresh", "sham_1948911": "Mush<PERSON>, Medium White", "sham_1955521": "Mushroom, Jumbo White", "sham_1979811": "Mushroom, Portabella Baby", "sham_2564351": "Mushroom, Maitake Cello Pack Refrigerated Fresh", "sham_2861001": "<PERSON><PERSON><PERSON>, Hen In The Woods 1lb", "sham_2955871": "Mushroom, Crimini", "sham_3005841": "Mushroom, Portabella", "sham_3162201": "Mushroom, Beech White", "sham_3162211": "Mushroom, Beech Brown", "sham_3350271": "Mushroom, Shiitake #2", "sham_3398551": "Mushroom, Shiitake Domestic", "sham_3401141": "Mushroom, King Oyster", "sham_4678451": "Mushroom, Oyster", "sham_4716271": "Mushroom, White Sliced 1/8\"", "sham_4796151": "Mushroom, Maitake", "sham_0100307": "Mush<PERSON>, Crimini Sliced 1/4\"", "sham_0026307": "Mushroom, Lion Mane Box Refrigerated Fresh", "sysco_1182211": "Mushroom Medium Fresh", "sysco_1182229": "Mushrooms Button Fresh", "sysco_1300391": "Mushroom Sliced Fresh", "sysco_4330320": "Mushroom Port Baby W/stem Fresh", "sysco_4754909": "Mushrooms Crimini Fresh Organic", "sysco_0384655": "Mushroom Crimini <PERSON> Fresh", "usfood_5733860": "Mushroom, Crimini #1 Grade Cultured Fresh Ref Box", "usfood_1419332": "Mushroom, Portabella Medium 4-5\" W/ Stem Fresh Ref", "usfood_5733829": "Mushroom, Shiitake Fresh Ref", "usfood_1355551": "Mush<PERSON>, <PERSON> Button Small #1 Grade Fresh Ref", "usfood_5729603": "Mush<PERSON>, White Sliced 1/8\" Fresh Ref", "usfood_7331499": "Mushroom, Medium #1 Grade Box Fresh Ref", "usfood_5419338": "Mushroom, Portabella Small Fresh Ref", "usfood_4355525": "Mushroom, Random Size Foodservice Fresh Ref", "usfood_6331508": "Mushroom, White Medium #1 Grade Box Fresh Ref", "usfood_5728837": "Mush<PERSON>, White Sliced 3/16\" Fresh Ref", "usfood_6384895": "Mushroom, Crimini Sliced Fresh Ref", "usfood_1384783": "Mushroom, Portabella #2 Grade Fresh Ref", "perf_931095": "MUSHROOM BUTTON SMALL", "greco_60539": "MUSHROOM CRIMINI 10#", "greco_60541": "MUSHROOM CRIMINI 5#", "greco_63078": "MUSHROOM CRIMINI LARGE 10#", "depot_59232": "Whole Cremini Mushrooms - 10 lb", "perf_886227": "MUSHROOM CRIMINI BABY BELLA", "greco_62829P": "MUSHROOM OYSTER CS", "perf_607931": "MUSHROOM OYSTER", "depot_42835": "White Medium Mushrooms 10 lbs", "perf_957713": "MUSHROOM MEDIUM", "greco_63155": "MUSHROOM BROWN BEECH 24 CT CS", "greco_60538": "MUSHROOM WHT SLICED CS", "greco_63627": "MUSHROOM QB SLICED 20#", "depot_42772": "Sliced <PERSON><PERSON><PERSON> - 10 lbs", "perf_396927": "MUSHROOM SLICED SAUTEED ROASTED", "perf_23666": "MUSHROOM SLICED", "greco_63624": "MUSHROOM CRIMINI SLICED 10#", "depot_2620038": "Shiitake Mushroom Strips, 3 lb", "perf_119659": "MUSHROOM FOODSERVICE", "usfood_6127849": "Mushroom, Button Whole Large 100-150 Count Canned Water", "chef_7063514": "Portobello Mushrooms Fresh", "sham_1930381": "Mushroom, Whole Straw Peeled Imported", "usfood_5007494": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (White Button), Sliced, Organic", "usfood_3481794": "Mushroom, Trumpet Black Whole Dried", "chef_3290657": "Mushroom Medium Fresh"}, "error": "Invalid JSON response: Expected double-quoted property name in JSON at position 4618 (line 120 column 5)", "timestamp": "2025-07-01T09:55:35.083Z"}, {"arrayIndex": 794, "objectNumber": 795, "object": {"sham_4960011": "<PERSON><PERSON>, Cooked Peeled & Deveined Tail On 16-20 Phosphate Free Iqf", "chef_4075287": "Aqua Star 61-70 Cooked Peeled Cocktail Shrimp Tray", "sham_4959891": "<PERSON><PERSON>, <PERSON>ed & <PERSON>eined <PERSON>l Off 61-70 Phosphate Free Iqf", "sham_4959951": "Shrimp, Cooked Peeled & Deveined Tail Off 100-150 Phosphate Free", "sham_4959991": "<PERSON>mp, Cooked Peeled & Deveined Tail Off 60-100 Phosphate Free", "sham_4960031": "<PERSON><PERSON>, Cooked Peeled & Deveined Tail On 21-25 Phosphate Free Iqf", "sham_4960041": "<PERSON><PERSON>, Cooked Peeled & Deveined Tail On 26-30 Phosphate Free Iqf", "sham_4960061": "<PERSON><PERSON>, Cooked Peeled & Deveined Tail On 31-40 Phosphate Free Iqf", "sysco_7078951": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-on Cooked 21/25 Count", "sysco_7078993": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-on Cooked 16/20 Count", "sysco_9502485": "Shrimp White Cooked Peeled & Deveined Tail-on 26-30 Ct Farm Raised", "perf_421151": "<PERSON><PERSON><PERSON> WHITE 61-70 RAW_PEELED_DEVEINED_TAIL_OFF INDIA_ABBREV FROZEN FARMED PRODUCT INDIA PERU ECUADOR", "greco_206004": "SHRIMP CKD 31/40 P&D T-OFF", "depot_50357": "Co-Star Shrimp P&D Tail Off 90-130", "depot_2420074": "Frozen Shrimp - Cooked, <PERSON> & <PERSON>, Tail-off- 71-90 ct - 2 lb", "depot_3320006": "<PERSON>mp, Cooked Tail Off", "perf_292779": "SHRIMP 41-50 COOKED_PEELED_DEVEINED_TAIL_OFF THAILAND_ABBREV IN INDIVIDUALLY_QUICK_FROZEN VIETNAM THAILAND INDONESIA MALAYSIA", "depot_2870071": "<PERSON><PERSON> - <PERSON><PERSON> Shrimp, <PERSON> - 20/20 Ct", "depot_2420132": "Frozen Shrimp - Cooked, <PERSON> & <PERSON>, Tail-on- 16-20 ct - 2 lb", "depot_2430000": "CoStar- <PERSON><PERSON> Peeled and Detailed TF 26/30", "depot_2420115": "Frozen Shrimp - <PERSON>d, <PERSON> & <PERSON>, Tail-on- 31-40 ct - 2 lb", "depot_2420313": "Shrimp P&D Tail On 31-40", "chef_7355416": "Aqua Star Cooked Peeled Tail-On White Shrimp 16-20 Pc/Lb", "sysco_5107525": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-on 16/20 Count", "usfood_7940711": "<PERSON>mp, Cooked 16-20 <PERSON> Peeled-&-deveined <PERSON><PERSON>-on Iqf Frozen Imported As", "usfood_6723159": "<PERSON><PERSON>, Cooked 21-25 <PERSON> Peeled-&-deveined <PERSON><PERSON>-on Iqf <PERSON>ozen Imported As", "greco_20599": "SHRIMP 16-20 RAW PLD T-ON SUPREME", "greco_20599C": "SHRIMP WHT 16-20 RAW PLD T-ON 5/2#", "depot_2420223": "Shrimp, 16/20, raw, peeled, deveined, tail on- 2 lbs", "depot_45676": "<PERSON>, 16/20, <PERSON><PERSON> and <PERSON><PERSON> On - 2lb", "perf_421091": "SHRIMP WHITE 16-20 RAW_PEELED_DEVEINED_TAIL_ON FROZEN INDIA_ABBREV INDIA PERU OR ECUADOR FARM RAISED", "chef_4658598": "<PERSON><PERSON><PERSON> Cooked White Shrimp Peeled And Deveined", "sham_0035964": "<PERSON><PERSON>, <PERSON><PERSON> 13-15 <PERSON><PERSON> Peeled & Deveined Tail On Southeast Asia Farmed Bag Iqf", "sysco_5106388": "<PERSON><PERSON> Peeled And <PERSON><PERSON>ed <PERSON>-off 16/20 Count", "sysco_5106402": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-off 21/25 Count", "sysco_6731515": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-on 26/30 Count", "sysco_6734939": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-on 21/25 Count", "sysco_6739153": "<PERSON><PERSON> Peeled And <PERSON><PERSON>ed <PERSON>-off 26/30 Count", "sysco_7950306": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-on 31/40 Count", "sysco_7952310": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-off 71/90 Count", "sysco_7952468": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-off 31/40 Count", "sysco_7952492": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-off 41/50 Count", "sysco_7952526": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-off 51/60 Count", "sysco_7952559": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-off 61/70 Count", "sysco_9902824": "Shrimp White Cooked Peeled & Deveined 100-150 Ct Farm Raised", "usfood_3571130": "<PERSON><PERSON>, 41-50 <PERSON> Peeled-&-deveined Tail-off Cooked Frozen Farmed", "usfood_5746409": "<PERSON>mp, Cooked 20 Count White Peeled-&-deveined <PERSON>l-on Frozen Im", "greco_20642": "SHRIMP WHITE TAIL ON 13/15 20#", "perf_421117": "SHRIMP WHITE 16-20 RAW_PEELED_DEVEINED_TAIL_OFF INDIA_ABBREV FROZEN FARMED PRODUCT INDIA PERU OR ECUADOR", "greco_20606": "SHRIMP 21-25 P&D TAIL OFF IQF", "perf_421098": "SHRIMP WHITE 21-25 RAW_PEELED_DEVEINED_TAIL_OFF INDIA_ABBREV FROZEN FARMED PRODUCT INDIA PERU OR ECUADOR", "greco_20597C": "SHRIMP 26-30 RAW PLD T-ON 5/2#", "greco_20597P": "SHRIMP 26-30 RAW PLD T-ON 2#", "perf_421095": "SH<PERSON><PERSON> WHITE 26-30 RAW_PEELED_DEVEINED_TAIL_ON FROZEN INDIA_ABBREV FARMED PRODUCT OF INDIA PERU OR ECUADOR", "greco_20594": "SHRIMP 26-30 RAW PLD T-OFF 2#", "greco_20594C": "SHRIMP 26-30 RAW PLD T-OFF 5/2#", "perf_421121": "SH<PERSON><PERSON> WHITE 26-30 RAW_PEELED_DEVEINED_TAIL_OFF INDIA_ABBREV FROZEN FARMED PRODUCT INDIA PERU OR ECUADOR", "greco_2060001": "SHRIMP 31-40 WHT P&D T-ON RW", "greco_206332": "SHRIMP 70-90 RAW P&D TO #0100299", "perf_421153": "<PERSON><PERSON><PERSON> WHITE 71-90 RAW_PEELED_DEVEINED_TAIL_OFF INDIA_ABBREV FROZEN FARMED PRODUCT INDIA PERU OR ECUADOR", "greco_205502": "SHRIMP 31-40 RAW PLD T-OFF 5/2# BAG", "greco_205502P": "SHRIMP 31-40 RAW PLD T-OFF 2# BAG", "perf_421127": "SHRIMP WHITE 31-40 RAW_PEELED_DEVEINED_TAIL_OFF INDIA_ABBREV FROZEN FARMED PRODUCT INDIA PERU OR ECUADOR", "perf_421129": "SHRIMP WHITE 41-50 RAW_PEELED_DEVEINED_TAIL_OFF INDIA_ABBREV FROZEN PRODUCT OF INDIA PERU OR ECUADOR", "perf_348121": "<PERSON><PERSON><PERSON> WHITE 51-60 RAW_PEELED_DEVEINED_TAIL_OFF FROZEN IDENTIFICATION INDONESIA INDIA FARM THAILAND", "depot_2430084": "Frozen Shrimp - Cooked, Peeled & Undeveined, Tail-off- 150-250 ct- 5 lb", "sysco_6034049": "Shrimp Sushi Ebi 4l 20 Pc's/Tray 15 Trays/Cs 5 Lb Cs Frozen", "chef_3855185": "Aqua Star Peeled <PERSON>l On Shrimp Cooked 21-25", "chef_9203325": "Aqua Star Cooked Peeled <PERSON>-On White Shrimp 31-40", "sysco_0774069": "<PERSON><PERSON> Peeled And <PERSON><PERSON><PERSON>-on 13/15", "chef_6870027": "Aqua Star Shrimp Raw Peeled <PERSON>l On 16-20", "perf_421083": "SH<PERSON>MP WHITE 13-15 RAW_PEELED_DEVEINED_TAIL_ON FROZEN INDIA_ABBREV FARMED PRODUCT OF INDIA PERU OR ECUADOR", "perf_343832": "SH<PERSON>MP WHITE 13-15 RAW_PEELED_DEVEINED_TAIL_ON IN FROZEN INDIA FARM", "perf_360743": "SH<PERSON>MP WHITE 13-15 RAW_PEELED_DEVEINED_TAIL_ON FROZEN"}, "error": "Invalid JSON response: Expected double-quoted property name in JSON at position 6498 (line 151 column 5)", "timestamp": "2025-07-01T13:10:54.801Z"}]